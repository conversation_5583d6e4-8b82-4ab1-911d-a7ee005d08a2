const ServiceCard = ({ icon, title, description, delay = 0 }) => {
  return (
    <div
      className="relative bg-gradient-to-br from-primary via-primary to-primary/80 p-8 rounded-2xl border border-white/10 transition-all duration-500 hover:border-accent hover:-translate-y-4 hover:shadow-2xl hover:shadow-accent/30 group animate-fade-in-up overflow-hidden"
      style={{ animationDelay: `${delay}ms` }}
    >
      {/* Animated Background Gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-accent/10 via-transparent to-accent-hover/10 opacity-0 group-hover:opacity-100 transition-all duration-500 rounded-2xl"></div>

      {/* Floating Particles Effect */}
      <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-700">
        <div className="absolute top-4 right-4 w-1 h-1 bg-accent rounded-full animate-pulse"></div>
        <div className="absolute top-12 right-8 w-0.5 h-0.5 bg-accent-hover rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
        <div className="absolute bottom-8 left-6 w-1.5 h-1.5 bg-accent/60 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
      </div>

      {/* Glowing Border Effect */}
      <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-accent via-accent-hover to-accent opacity-0 group-hover:opacity-20 blur-sm transition-all duration-500 -z-10"></div>

      {/* Icon Container with Enhanced Effects */}
      <div className="relative z-10 mb-8">
        <div className="w-16 h-16 bg-gradient-to-br from-accent/20 to-accent-hover/20 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 group-hover:shadow-lg group-hover:shadow-accent/30">
          <div className="text-accent group-hover:text-accent-hover transition-colors duration-300 transform group-hover:scale-110">
            {icon}
          </div>
        </div>

        {/* Floating Icon Background */}
        <div className="absolute -top-2 -left-2 w-20 h-20 bg-gradient-to-br from-accent/5 to-transparent rounded-full opacity-0 group-hover:opacity-100 transition-all duration-500 blur-xl"></div>
      </div>

      {/* Title with Enhanced Typography */}
      <h3 className="relative z-10 text-xl font-bold mb-4 text-text-primary group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-accent group-hover:to-accent-hover group-hover:bg-clip-text transition-all duration-300">
        {title}
      </h3>

      {/* Description with Better Spacing */}
      <p className="relative z-10 text-text-secondary leading-relaxed group-hover:text-text-primary transition-colors duration-300 text-sm">
        {description}
      </p>

      {/* Interactive Arrow */}
      <div className="relative z-10 mt-6 flex items-center text-accent opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-0 group-hover:translate-x-2">
        <span className="text-sm font-medium mr-2">Explorar</span>
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
        </svg>
      </div>

      {/* Corner Accent */}
      <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-bl from-accent/10 to-transparent rounded-bl-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

      {/* Bottom Glow Effect */}
      <div className="absolute bottom-0 left-1/2 -translate-x-1/2 w-3/4 h-1 bg-gradient-to-r from-transparent via-accent to-transparent opacity-0 group-hover:opacity-60 transition-opacity duration-500 blur-sm"></div>
    </div>
  );
};

export default ServiceCard;
