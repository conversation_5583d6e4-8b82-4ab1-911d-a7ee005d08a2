import { contactContent } from '../../constants.jsx';
import SectionHeader from '../ui/SectionHeader';
import AnimatedButton from '../ui/AnimatedButton';

const ContactCTA = () => {
  return (
    <section id="contact" className="section-padding bg-primary/20 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[800px] h-[400px] rounded-full bg-gradient-to-r from-accent/10 to-accent-hover/10 blur-3xl animate-float"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,transparent_0%,rgba(10,10,10,0.8)_100%)]"></div>
      </div>
      
      <div className="container-custom relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          <SectionHeader 
            title={contactContent.title}
            subtitle={contactContent.subtitle}
          />
          
          {/* Main CTA */}
          <div className="mb-12">
            <AnimatedButton 
              href={`mailto:${contactContent.email}`}
              size="lg"
              className="animate-glow"
            >
              {contactContent.ctaText}
            </AnimatedButton>
          </div>
          
          {/* Contact Information */}
          <div className="glass-effect p-8 rounded-2xl">
            <div className="grid md:grid-cols-3 gap-8 text-center">
              <div>
                <div className="w-12 h-12 bg-accent/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 className="font-semibold text-text-primary mb-2">Email</h3>
                <p className="text-text-secondary text-sm">{contactContent.email}</p>
              </div>
              
              <div>
                <div className="w-12 h-12 bg-accent/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="font-semibold text-text-primary mb-2">Respuesta</h3>
                <p className="text-text-secondary text-sm">En menos de 24 horas</p>
              </div>
              
              <div>
                <div className="w-12 h-12 bg-accent/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="font-semibold text-text-primary mb-2">Consulta</h3>
                <p className="text-text-secondary text-sm">Completamente gratuita</p>
              </div>
            </div>
          </div>
          
          {/* Additional CTA Text */}
          <div className="mt-12">
            <p className="text-text-secondary text-lg mb-4">
              ¿Prefieres una llamada? Agenda una consulta gratuita
            </p>
            <AnimatedButton 
              variant="secondary"
              size="md"
            >
              Agendar Llamada
            </AnimatedButton>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactCTA;
