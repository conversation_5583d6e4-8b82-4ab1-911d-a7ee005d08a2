@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import "tailwindcss";

@layer base {
  body {
    background-color: #0A0A0A;
    color: #F3F4F6;
    font-family: 'Inter', sans-serif;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .glass-effect {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .gradient-text {
    background: linear-gradient(to right, #A78BFA, #C4B5FD);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
  }

  .section-padding {
    padding: 5rem 1rem;
  }

  .container-custom {
    max-width: 80rem;
    margin: 0 auto;
  }

  @media (min-width: 640px) {
    .section-padding {
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .section-padding {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }

  /* Enhanced Service Card Animations */
  .service-card-hover {
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .service-card-hover:hover {
    transform: translateY(-16px) scale(1.02);
  }

  /* Glowing Effect */
  .glow-effect {
    position: relative;
  }

  .glow-effect::before {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(45deg, #A78BFA, #C4B5FD, #A78BFA);
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.5s ease;
    z-index: -1;
    filter: blur(8px);
  }

  .glow-effect:hover::before {
    opacity: 0.7;
  }

  /* Particle Animation */
  @keyframes particle-float {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
      opacity: 0.6;
    }
    50% {
      transform: translateY(-10px) rotate(180deg);
      opacity: 1;
    }
  }

  .particle-animate {
    animation: particle-float 3s ease-in-out infinite;
  }
}