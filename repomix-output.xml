This file is a merged representation of a subset of the codebase, containing specifically included files, combined into a single document by Repomix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of a subset of the repository's contents that is considered the most important context.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Only files matching these patterns are included: src
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

</file_summary>

<directory_structure>
src/App.jsx
src/assets/react.svg
src/components/layout/Footer.jsx
src/components/layout/Navbar.jsx
src/components/sections/CaseStudies.jsx
src/components/sections/ContactCTA.jsx
src/components/sections/Hero.jsx
src/components/sections/Services.jsx
src/components/ui/AnimatedButton.jsx
src/components/ui/CaseStudyCard.jsx
src/components/ui/SectionHeader.jsx
src/components/ui/ServiceCard.jsx
src/constants.jsx
src/index.css
src/main.jsx
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="src/App.jsx">
import Navbar from './components/layout/Navbar';
import Hero from './components/sections/Hero';
import Services from './components/sections/Services';
import CaseStudies from './components/sections/CaseStudies';
import ContactCTA from './components/sections/ContactCTA';
import Footer from './components/layout/Footer';

function App() {
  return (
    <div className="relative">
      {/* Global Decorative Background */}
      <div className="absolute inset-0 -z-10 h-full w-full bg-background bg-[linear-gradient(to_right,#8080800a_1px,transparent_1px),linear-gradient(to_bottom,#8080800a_1px,transparent_1px)] bg-[size:14px_24px]"></div>
      <div className="absolute left-0 right-0 top-0 -z-10 m-auto h-[310px] w-[310px] rounded-full bg-accent opacity-20 blur-[100px]"></div>

      <Navbar />
      <main>
        <Hero />
        <Services />
        <CaseStudies />
        <ContactCTA />
      </main>
      <Footer />
    </div>
  );
}

export default App;
</file>

<file path="src/assets/react.svg">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" class="iconify iconify--logos" width="35.93" height="32" preserveAspectRatio="xMidYMid meet" viewBox="0 0 256 228"><path fill="#00D8FF" d="M210.483 73.824a171.49 171.49 0 0 0-8.24-2.597c.465-1.9.893-3.777 1.273-5.621c6.238-30.281 2.16-54.676-11.769-62.708c-13.355-7.7-35.196.329-57.254 19.526a171.23 171.23 0 0 0-6.375 5.848a155.866 155.866 0 0 0-4.241-3.917C100.759 3.829 77.587-4.822 63.673 3.233C50.33 10.957 46.379 33.89 51.995 62.588a170.974 170.974 0 0 0 1.892 8.48c-3.28.932-6.445 1.924-9.474 2.98C17.309 83.498 0 98.307 0 113.668c0 15.865 18.582 31.778 46.812 41.427a145.52 145.52 0 0 0 6.921 2.165a167.467 167.467 0 0 0-2.01 9.138c-5.354 28.2-1.173 50.591 12.134 58.266c13.744 7.926 36.812-.22 59.273-19.855a145.567 145.567 0 0 0 5.342-4.923a168.064 168.064 0 0 0 6.92 6.314c21.758 18.722 43.246 26.282 56.54 18.586c13.731-7.949 18.194-32.003 12.4-61.268a145.016 145.016 0 0 0-1.535-6.842c1.62-.48 3.21-.974 4.76-1.488c29.348-9.723 48.443-25.443 48.443-41.52c0-15.417-17.868-30.326-45.517-39.844Zm-6.365 70.984c-1.4.463-2.836.91-4.3 1.345c-3.24-10.257-7.612-21.163-12.963-32.432c5.106-11 9.31-21.767 12.459-31.957c2.619.758 5.16 1.557 7.61 2.4c23.69 8.156 38.14 20.213 38.14 29.504c0 9.896-15.606 22.743-40.946 31.14Zm-10.514 20.834c2.562 12.94 2.927 24.64 1.23 33.787c-1.524 8.219-4.59 13.698-8.382 15.893c-8.067 4.67-25.32-1.4-43.927-17.412a156.726 156.726 0 0 1-6.437-5.87c7.214-7.889 14.423-17.06 21.459-27.246c12.376-1.098 24.068-2.894 34.671-5.345a134.17 134.17 0 0 1 1.386 6.193ZM87.276 214.515c-7.882 2.783-14.16 2.863-17.955.675c-8.075-4.657-11.432-22.636-6.853-46.752a156.923 156.923 0 0 1 1.869-8.499c10.486 2.32 22.093 3.988 34.498 4.994c7.084 9.967 14.501 19.128 21.976 27.15a134.668 134.668 0 0 1-4.877 4.492c-9.933 8.682-19.886 14.842-28.658 17.94ZM50.35 144.747c-12.483-4.267-22.792-9.812-29.858-15.863c-6.35-5.437-9.555-10.836-9.555-15.216c0-9.322 13.897-21.212 37.076-29.293c2.813-.98 5.757-1.905 8.812-2.773c3.204 10.42 7.406 21.315 12.477 32.332c-5.137 11.18-9.399 22.249-12.634 32.792a134.718 134.718 0 0 1-6.318-1.979Zm12.378-84.26c-4.811-24.587-1.616-43.134 6.425-47.789c8.564-4.958 27.502 2.111 47.463 19.835a144.318 144.318 0 0 1 3.841 3.545c-7.438 7.987-14.787 17.08-21.808 26.988c-12.04 1.116-23.565 2.908-34.161 5.309a160.342 160.342 0 0 1-1.76-7.887Zm110.427 27.268a347.8 347.8 0 0 0-7.785-12.803c8.168 1.033 15.994 2.404 23.343 4.08c-2.206 7.072-4.956 14.465-8.193 22.045a381.151 381.151 0 0 0-7.365-13.322Zm-45.032-43.861c5.044 5.465 10.096 11.566 15.065 18.186a322.04 322.04 0 0 0-30.257-.006c4.974-6.559 10.069-12.652 15.192-18.18ZM82.802 87.83a323.167 323.167 0 0 0-7.227 13.238c-3.184-7.553-5.909-14.98-8.134-22.152c7.304-1.634 15.093-2.97 23.209-3.984a321.524 321.524 0 0 0-7.848 12.897Zm8.081 65.352c-8.385-.936-16.291-2.203-23.593-3.793c2.26-7.3 5.045-14.885 8.298-22.6a321.187 321.187 0 0 0 7.257 13.246c2.594 4.48 5.28 8.868 8.038 13.147Zm37.542 31.03c-5.184-5.592-10.354-11.779-15.403-18.433c4.902.192 9.899.29 14.978.29c5.218 0 10.376-.117 15.453-.343c-4.985 6.774-10.018 12.97-15.028 18.486Zm52.198-57.817c3.422 7.8 6.306 15.345 8.596 22.52c-7.422 1.694-15.436 3.058-23.88 4.071a382.417 382.417 0 0 0 7.859-13.026a347.403 347.403 0 0 0 7.425-13.565Zm-16.898 8.101a358.557 358.557 0 0 1-12.281 19.815a329.4 329.4 0 0 1-23.444.823c-7.967 0-15.716-.248-23.178-.732a310.202 310.202 0 0 1-12.513-19.846h.001a307.41 307.41 0 0 1-10.923-20.627a310.278 310.278 0 0 1 10.89-20.637l-.001.001a307.318 307.318 0 0 1 12.413-19.761c7.613-.576 15.42-.876 23.31-.876H128c7.926 0 15.743.303 23.354.883a329.357 329.357 0 0 1 12.335 19.695a358.489 358.489 0 0 1 11.036 20.54a329.472 329.472 0 0 1-11 20.722Zm22.56-122.124c8.572 4.944 11.906 24.881 6.52 51.026c-.344 1.668-.73 3.367-1.15 5.09c-10.622-2.452-22.155-4.275-34.23-5.408c-7.034-10.017-14.323-19.124-21.64-27.008a160.789 160.789 0 0 1 5.888-5.4c18.9-16.447 36.564-22.941 44.612-18.3ZM128 90.808c12.625 0 22.86 10.235 22.86 22.86s-10.235 22.86-22.86 22.86s-22.86-10.235-22.86-22.86s10.235-22.86 22.86-22.86Z"></path></svg>
</file>

<file path="src/components/layout/Footer.jsx">
import { companyInfo } from '../../constants.jsx';

const Footer = () => {
  return (
    <footer className="py-8 border-t border-white/10 bg-primary/50">
      <div className="container-custom">
        <div className="text-center">
          <div className="mb-4">
            <div className="text-xl font-bold gradient-text mb-2">
              {companyInfo.name}
            </div>
            <p className="text-text-secondary text-sm">
              {companyInfo.tagline}
            </p>
          </div>
          
          <div className="flex justify-center space-x-6 mb-6">
            {/* Social Links Placeholder */}
            <div className="flex space-x-4">
              <div className="w-8 h-8 rounded-full bg-accent/20 flex items-center justify-center hover:bg-accent/30 transition-colors duration-200 cursor-pointer">
                <svg className="w-4 h-4 text-accent" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                </svg>
              </div>
              <div className="w-8 h-8 rounded-full bg-accent/20 flex items-center justify-center hover:bg-accent/30 transition-colors duration-200 cursor-pointer">
                <svg className="w-4 h-4 text-accent" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                </svg>
              </div>
              <div className="w-8 h-8 rounded-full bg-accent/20 flex items-center justify-center hover:bg-accent/30 transition-colors duration-200 cursor-pointer">
                <svg className="w-4 h-4 text-accent" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                </svg>
              </div>
            </div>
          </div>
          
          <p className="text-text-secondary text-sm">
            © {companyInfo.year} {companyInfo.name}. Todos los derechos reservados.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
</file>

<file path="src/components/layout/Navbar.jsx">
import { useState, useEffect } from 'react';
import { navLinks, companyInfo } from '../../constants.jsx';

const Navbar = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
      setIsMobileMenuOpen(false);
    }
  };

  return (
    <nav className={`fixed w-full top-0 z-50 transition-all duration-300 ${
      isScrolled 
        ? 'py-2 bg-background/80 backdrop-blur-lg border-b border-white/10' 
        : 'py-4 bg-transparent'
    }`}>
      <div className="container-custom">
        <div className="flex justify-between items-center">
          {/* Logo */}
          <div className="text-2xl font-bold gradient-text animate-glow">
            {companyInfo.name}
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex space-x-8">
            {navLinks.map((link) => (
              <button
                key={link.id}
                onClick={() => scrollToSection(link.id)}
                className="text-text-secondary hover:text-accent transition-all duration-300 font-medium relative group"
              >
                {link.title}
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-accent transition-all duration-300 group-hover:w-full"></span>
              </button>
            ))}
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden text-text-primary hover:text-accent transition-colors duration-200"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              {isMobileMenuOpen ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              )}
            </svg>
          </button>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden mt-4 glass-effect rounded-lg p-4 animate-fade-in-up">
            {navLinks.map((link) => (
              <button
                key={link.id}
                onClick={() => scrollToSection(link.id)}
                className="block w-full text-left py-2 text-text-secondary hover:text-accent transition-colors duration-200"
              >
                {link.title}
              </button>
            ))}
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navbar;
</file>

<file path="src/components/sections/CaseStudies.jsx">
import { caseStudies } from '../../constants.jsx';
import SectionHeader from '../ui/SectionHeader';
import CaseStudyCard from '../ui/CaseStudyCard';

const CaseStudies = () => {
  return (
    <section id="case-studies" className="section-padding bg-background relative">
      {/* Background Decoration */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-accent to-transparent"></div>
        <div className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-accent to-transparent"></div>
      </div>
      
      <div className="container-custom relative z-10">
        <SectionHeader 
          title="Clientes que ya están en el Futuro"
          subtitle="Descubre cómo nuestras soluciones de IA han transformado negocios reales, generando resultados medibles y un impacto duradero."
        />
        
        <div className="space-y-0">
          {caseStudies.map((study, index) => (
            <CaseStudyCard 
              key={study.id}
              image={study.image}
              title={study.title}
              description={study.description}
              quote={study.quote}
              author={study.author}
              metrics={study.metrics}
              reverse={index % 2 !== 0}
            />
          ))}
        </div>
        
        {/* Call to Action */}
        <div className="text-center mt-16">
          <div className="glass-effect p-8 rounded-2xl max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold mb-4 gradient-text">
              ¿Tu negocio será el próximo caso de éxito?
            </h3>
            <p className="text-text-secondary mb-6">
              Únete a empresas líderes que ya están aprovechando el poder de la IA para crecer y destacar en su industria.
            </p>
            <div className="flex justify-center space-x-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-accent">100%</div>
                <div className="text-sm text-text-secondary">Satisfacción</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-accent">24/7</div>
                <div className="text-sm text-text-secondary">Soporte</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-accent">30+</div>
                <div className="text-sm text-text-secondary">Proyectos</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CaseStudies;
</file>

<file path="src/components/sections/ContactCTA.jsx">
import { contactContent } from '../../constants.jsx';
import SectionHeader from '../ui/SectionHeader';
import AnimatedButton from '../ui/AnimatedButton';

const ContactCTA = () => {
  return (
    <section id="contact" className="section-padding bg-primary/20 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[800px] h-[400px] rounded-full bg-gradient-to-r from-accent/10 to-accent-hover/10 blur-3xl animate-float"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,transparent_0%,rgba(10,10,10,0.8)_100%)]"></div>
      </div>
      
      <div className="container-custom relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          <SectionHeader 
            title={contactContent.title}
            subtitle={contactContent.subtitle}
          />
          
          {/* Main CTA */}
          <div className="mb-12">
            <AnimatedButton 
              href={`mailto:${contactContent.email}`}
              size="lg"
              className="animate-glow"
            >
              {contactContent.ctaText}
            </AnimatedButton>
          </div>
          
          {/* Contact Information */}
          <div className="glass-effect p-8 rounded-2xl">
            <div className="grid md:grid-cols-3 gap-8 text-center">
              <div>
                <div className="w-12 h-12 bg-accent/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 className="font-semibold text-text-primary mb-2">Email</h3>
                <p className="text-text-secondary text-sm">{contactContent.email}</p>
              </div>
              
              <div>
                <div className="w-12 h-12 bg-accent/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="font-semibold text-text-primary mb-2">Respuesta</h3>
                <p className="text-text-secondary text-sm">En menos de 24 horas</p>
              </div>
              
              <div>
                <div className="w-12 h-12 bg-accent/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-6 h-6 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="font-semibold text-text-primary mb-2">Consulta</h3>
                <p className="text-text-secondary text-sm">Completamente gratuita</p>
              </div>
            </div>
          </div>
          
          {/* Additional CTA Text */}
          <div className="mt-12">
            <p className="text-text-secondary text-lg mb-4">
              ¿Prefieres una llamada? Agenda una consulta gratuita
            </p>
            <AnimatedButton 
              variant="secondary"
              size="md"
            >
              Agendar Llamada
            </AnimatedButton>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactCTA;
</file>

<file path="src/components/sections/Hero.jsx">
import { heroContent } from '../../constants.jsx';
import AnimatedButton from '../ui/AnimatedButton';

const Hero = () => {
  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0">
        {/* Grid Pattern */}
        <div className="absolute inset-0 bg-[linear-gradient(to_right,#8080800a_1px,transparent_1px),linear-gradient(to_bottom,#8080800a_1px,transparent_1px)] bg-[size:14px_24px]"></div>
        
        {/* Floating Orbs */}
        <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-accent rounded-full animate-subtle-pulse"></div>
        <div className="absolute top-1/3 right-1/3 w-1 h-1 bg-accent-hover rounded-full animate-subtle-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-accent rounded-full animate-subtle-pulse" style={{ animationDelay: '0.5s' }}></div>
        <div className="absolute top-1/2 right-1/4 w-1 h-1 bg-accent-hover rounded-full animate-subtle-pulse" style={{ animationDelay: '0.7s' }}></div>
        <div className="absolute bottom-1/3 right-1/2 w-2 h-2 bg-accent rounded-full animate-subtle-pulse" style={{ animationDelay: '0.3s' }}></div>
        
        {/* Large Gradient Orb */}
        <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] rounded-full bg-gradient-to-r from-accent/10 to-accent-hover/10 blur-3xl animate-float"></div>
      </div>
      
      {/* Content */}
      <div className="relative z-10 text-center max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 animate-fade-in-up">
          {heroContent.title}{' '}
          <span className="gradient-text">
            {heroContent.titleAccent}
          </span>
        </h1>
        
        <p className="text-xl md:text-2xl text-text-secondary mb-12 max-w-3xl mx-auto leading-relaxed animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
          {heroContent.subtitle}
        </p>
        
        <div className="animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
          <AnimatedButton 
            onClick={() => scrollToSection('services')}
            size="lg"
            className="animate-glow"
          >
            {heroContent.ctaText}
          </AnimatedButton>
        </div>
        
        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-accent rounded-full flex justify-center">
            <div className="w-1 h-3 bg-accent rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
</file>

<file path="src/components/sections/Services.jsx">
import { services } from '../../constants.jsx';
import SectionHeader from '../ui/SectionHeader';
import ServiceCard from '../ui/ServiceCard';

const Services = () => {
  return (
    <section id="services" className="section-padding bg-gradient-to-b from-background via-primary/20 to-background relative overflow-hidden">
      {/* Enhanced Background Effects */}
      <div className="absolute inset-0">
        {/* Animated Grid Pattern */}
        <div className="absolute inset-0 bg-[linear-gradient(to_right,#A78BFA0a_1px,transparent_1px),linear-gradient(to_bottom,#A78BFA0a_1px,transparent_1px)] bg-[size:40px_40px] animate-pulse"></div>

        {/* Floating Orbs */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-accent/10 rounded-full blur-2xl animate-float"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-accent-hover/10 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }}></div>

        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-accent/5 to-transparent"></div>
      </div>

      <div className="container-custom relative z-10">
        <SectionHeader
          title="Nuestros Servicios de Automatización"
          subtitle="Transformamos tu negocio con soluciones de IA personalizadas que impulsan el crecimiento y optimizan la eficiencia operativa."
        />

        {/* Enhanced Grid with Better Spacing */}
        <div className="grid md:grid-cols-3 gap-8 lg:gap-12 mb-16">
          {services.map((service, index) => (
            <ServiceCard
              key={service.id}
              icon={service.icon}
              title={service.title}
              description={service.description}
              delay={index * 200}
            />
          ))}
        </div>

        {/* Enhanced Bottom Section */}
        <div className="text-center">
          {/* Stats Section */}
          <div className="grid grid-cols-3 gap-8 mb-12 max-w-2xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-bold gradient-text mb-2">100%</div>
              <div className="text-text-secondary text-sm">Automatización</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold gradient-text mb-2">24/7</div>
              <div className="text-text-secondary text-sm">Disponibilidad</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold gradient-text mb-2">∞</div>
              <div className="text-text-secondary text-sm">Escalabilidad</div>
            </div>
          </div>

          {/* Decorative Separator */}
          <div className="flex items-center justify-center space-x-4 text-text-secondary">
            <div className="w-16 h-px bg-gradient-to-r from-transparent via-accent to-accent"></div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-accent rounded-full animate-pulse"></div>
              <span className="text-sm font-medium tracking-wider">SOLUCIONES PERSONALIZADAS</span>
              <div className="w-2 h-2 bg-accent rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
            </div>
            <div className="w-16 h-px bg-gradient-to-l from-accent via-accent to-transparent"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Services;
</file>

<file path="src/components/ui/AnimatedButton.jsx">
const AnimatedButton = ({ 
  children, 
  onClick, 
  href, 
  variant = 'primary', 
  size = 'md',
  className = '' 
}) => {
  const baseClasses = "font-bold rounded-lg transition-all duration-300 transform hover:scale-105 relative overflow-hidden group";
  
  const variants = {
    primary: "bg-accent text-white hover:bg-accent-hover shadow-lg shadow-accent/30 hover:shadow-accent/50",
    secondary: "bg-transparent border-2 border-accent text-accent hover:bg-accent hover:text-white",
    ghost: "bg-white/5 text-text-primary hover:bg-white/10 border border-white/10 hover:border-accent"
  };
  
  const sizes = {
    sm: "py-2 px-4 text-sm",
    md: "py-3 px-8 text-base",
    lg: "py-4 px-12 text-xl"
  };

  const classes = `${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`;

  const ButtonContent = () => (
    <>
      <span className="relative z-10">{children}</span>
      <div className="absolute inset-0 bg-gradient-to-r from-accent-hover to-accent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
    </>
  );

  if (href) {
    return (
      <a href={href} className={classes}>
        <ButtonContent />
      </a>
    );
  }

  return (
    <button onClick={onClick} className={classes}>
      <ButtonContent />
    </button>
  );
};

export default AnimatedButton;
</file>

<file path="src/components/ui/CaseStudyCard.jsx">
const CaseStudyCard = ({ 
  image, 
  title, 
  description, 
  quote, 
  author, 
  metrics = [], 
  reverse = false 
}) => {
  return (
    <div className="mb-20 animate-fade-in-up">
      <div className="grid md:grid-cols-2 gap-12 items-center">
        {/* Image */}
        <div className={`${reverse ? 'md:order-last' : ''}`}>
          <div className="relative group">
            <img 
              src={image} 
              alt={title}
              className="rounded-lg shadow-lg w-full h-64 object-cover transition-transform duration-300 group-hover:scale-105"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-accent/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></div>
          </div>
        </div>
        
        {/* Content */}
        <div className={`${reverse ? 'md:order-first' : ''}`}>
          <h3 className="text-2xl font-bold mb-4 text-text-primary">
            {title}
          </h3>
          
          <p className="text-text-secondary mb-6 text-lg leading-relaxed">
            {description}
          </p>
          
          {/* Metrics */}
          {metrics.length > 0 && (
            <div className="grid grid-cols-3 gap-4 mb-6">
              {metrics.map((metric, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl font-bold text-accent mb-1">
                    {metric.value}
                  </div>
                  <div className="text-sm text-text-secondary">
                    {metric.label}
                  </div>
                </div>
              ))}
            </div>
          )}
          
          {/* Quote */}
          <blockquote className="border-l-4 border-accent pl-4 italic text-accent bg-accent/5 p-4 rounded-r-lg">
            <p className="mb-2">"{quote}"</p>
            <cite className="text-sm text-text-secondary not-italic">
              — {author}
            </cite>
          </blockquote>
        </div>
      </div>
    </div>
  );
};

export default CaseStudyCard;
</file>

<file path="src/components/ui/SectionHeader.jsx">
const SectionHeader = ({ title, subtitle, centered = true }) => {
  return (
    <div className={`mb-16 ${centered ? 'text-center' : ''}`}>
      <h2 className="text-3xl md:text-4xl font-bold mb-4 gradient-text animate-fade-in-up">
        {title}
      </h2>
      {subtitle && (
        <p className="text-xl text-text-secondary max-w-3xl mx-auto animate-fade-in-up">
          {subtitle}
        </p>
      )}
    </div>
  );
};

export default SectionHeader;
</file>

<file path="src/components/ui/ServiceCard.jsx">
const ServiceCard = ({ icon, title, description, delay = 0 }) => {
  return (
    <div
      className="relative bg-gradient-to-br from-primary via-primary to-primary/80 p-8 rounded-2xl border border-white/10 transition-all duration-500 hover:border-accent hover:-translate-y-4 hover:shadow-2xl hover:shadow-accent/30 group animate-fade-in-up overflow-hidden"
      style={{ animationDelay: `${delay}ms` }}
    >
      {/* Animated Background Gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-accent/10 via-transparent to-accent-hover/10 opacity-0 group-hover:opacity-100 transition-all duration-500 rounded-2xl"></div>

      {/* Floating Particles Effect */}
      <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-700">
        <div className="absolute top-4 right-4 w-1 h-1 bg-accent rounded-full animate-pulse"></div>
        <div className="absolute top-12 right-8 w-0.5 h-0.5 bg-accent-hover rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
        <div className="absolute bottom-8 left-6 w-1.5 h-1.5 bg-accent/60 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
      </div>

      {/* Glowing Border Effect */}
      <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-accent via-accent-hover to-accent opacity-0 group-hover:opacity-20 blur-sm transition-all duration-500 -z-10"></div>

      {/* Icon Container with Enhanced Effects */}
      <div className="relative z-10 mb-8">
        <div className="w-16 h-16 bg-gradient-to-br from-accent/20 to-accent-hover/20 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 group-hover:shadow-lg group-hover:shadow-accent/30">
          <div className="text-accent group-hover:text-accent-hover transition-colors duration-300 transform group-hover:scale-110">
            {icon}
          </div>
        </div>

        {/* Floating Icon Background */}
        <div className="absolute -top-2 -left-2 w-20 h-20 bg-gradient-to-br from-accent/5 to-transparent rounded-full opacity-0 group-hover:opacity-100 transition-all duration-500 blur-xl"></div>
      </div>

      {/* Title with Enhanced Typography */}
      <h3 className="relative z-10 text-xl font-bold mb-4 text-text-primary group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-accent group-hover:to-accent-hover group-hover:bg-clip-text transition-all duration-300">
        {title}
      </h3>

      {/* Description with Better Spacing */}
      <p className="relative z-10 text-text-secondary leading-relaxed group-hover:text-text-primary transition-colors duration-300 text-sm">
        {description}
      </p>

      {/* Interactive Arrow */}
      <div className="relative z-10 mt-6 flex items-center text-accent opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-0 group-hover:translate-x-2">
        <span className="text-sm font-medium mr-2">Explorar</span>
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
        </svg>
      </div>

      {/* Corner Accent */}
      <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-bl from-accent/10 to-transparent rounded-bl-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

      {/* Bottom Glow Effect */}
      <div className="absolute bottom-0 left-1/2 -translate-x-1/2 w-3/4 h-1 bg-gradient-to-r from-transparent via-accent to-transparent opacity-0 group-hover:opacity-60 transition-opacity duration-500 blur-sm"></div>
    </div>
  );
};

export default ServiceCard;
</file>

<file path="src/constants.jsx">
export const navLinks = [
  { id: 'services', title: 'Servicios' },
  { id: 'case-studies', title: 'Casos de Éxito' },
  { id: 'contact', title: 'Contacto' },
];

export const services = [
  {
    id: 'chatbots',
    icon: (
      <svg className="w-12 h-12" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
      </svg>
    ),
    title: 'Chatbots Inteligentes 24/7',
    description: 'Creamos chatbots que mejoran la atención al cliente, responden preguntas frecuentes y gestionan reservas, funcionando sin descanso.'
  },
  {
    id: 'content-creation',
    icon: (
      <svg className="w-12 h-12" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
      </svg>
    ),
    title: 'Creación de Contenido con IA',
    description: 'Generamos contenido atractivo y relevante para tus redes sociales, manteniendo tu marca activa y captando nuevos clientes.'
  },
  {
    id: 'email-automation',
    icon: (
      <svg className="w-12 h-12" fill="currentColor" viewBox="0 0 20 20">
        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
      </svg>
    ),
    title: 'Automatización de Email Marketing',
    description: 'Diseñamos secuencias de correos automáticas para nutrir a tus leads, recuperar carritos abandonados y fidelizar a tus clientes.'
  }
];

export const caseStudies = [
  {
    id: 'dental-clinic',
    image: 'https://images.unsplash.com/photo-1629909613654-28e377c37b09?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
    title: 'Clínica Dental Sonrisa Sana',
    description: 'Implementamos un chatbot que agenda citas y responde al 80% de las consultas, reduciendo la carga administrativa en un 30% y aumentando la satisfacción del paciente.',
    quote: 'Automatia nos ha liberado para centrarnos en nuestros pacientes. ¡Un cambio radical!',
    author: 'Dra. Eva Ruiz',
    metrics: [
      { label: 'Reducción administrativa', value: '30%' },
      { label: 'Consultas automatizadas', value: '80%' },
      { label: 'Satisfacción del paciente', value: '+25%' }
    ]
  },
  {
    id: 'restaurant',
    image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
    title: 'Restaurante FusiónGourmet',
    description: 'Desarrollamos un sistema de IA que genera y programa publicaciones semanales en redes sociales, logrando un aumento del 50% en la interacción y un 15% más de reservas online.',
    quote: 'Nuestras redes nunca han estado mejor. ¡Y todo en piloto automático!',
    author: 'Chef Marco Rossi',
    metrics: [
      { label: 'Aumento en interacción', value: '50%' },
      { label: 'Más reservas online', value: '15%' },
      { label: 'Tiempo ahorrado', value: '10h/semana' }
    ]
  }
];

export const heroContent = {
  title: 'Automatizamos tu éxito con',
  titleAccent: 'Inteligencia Artificial',
  subtitle: 'Soluciones de IA a medida para restaurantes, clínicas y comercios que quieren liderar el futuro.',
  ctaText: 'Descubre Cómo'
};

export const contactContent = {
  title: '¿Listo para Automatizar tu Negocio?',
  subtitle: 'Hablemos de cómo la IA puede transformar tu empresa. El primer paso hacia el futuro está a un solo clic de distancia.',
  ctaText: 'Enviar un Email',
  email: '<EMAIL>'
};

export const companyInfo = {
  name: 'Automatia',
  year: '2025',
  tagline: 'El futuro de la automatización empresarial'
};
</file>

<file path="src/index.css">
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import "tailwindcss";

@layer base {
  body {
    background-color: #0A0A0A;
    color: #F3F4F6;
    font-family: 'Inter', sans-serif;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .glass-effect {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .gradient-text {
    background: linear-gradient(to right, #A78BFA, #C4B5FD);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
  }

  .section-padding {
    padding: 5rem 1rem;
  }

  .container-custom {
    max-width: 80rem;
    margin: 0 auto;
  }

  @media (min-width: 640px) {
    .section-padding {
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .section-padding {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }

  /* Enhanced Service Card Animations */
  .service-card-hover {
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .service-card-hover:hover {
    transform: translateY(-16px) scale(1.02);
  }

  /* Glowing Effect */
  .glow-effect {
    position: relative;
  }

  .glow-effect::before {
    content: '';
    position: absolute;
    inset: -2px;
    background: linear-gradient(45deg, #A78BFA, #C4B5FD, #A78BFA);
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.5s ease;
    z-index: -1;
    filter: blur(8px);
  }

  .glow-effect:hover::before {
    opacity: 0.7;
  }

  /* Particle Animation */
  @keyframes particle-float {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
      opacity: 0.6;
    }
    50% {
      transform: translateY(-10px) rotate(180deg);
      opacity: 1;
    }
  }

  .particle-animate {
    animation: particle-float 3s ease-in-out infinite;
  }
}
</file>

<file path="src/main.jsx">
import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.jsx'

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <App />
  </StrictMode>,
)
</file>

</files>
