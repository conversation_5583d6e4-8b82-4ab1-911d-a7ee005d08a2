import { caseStudies } from '../../constants.jsx';
import SectionHeader from '../ui/SectionHeader';
import CaseStudyCard from '../ui/CaseStudyCard';

const CaseStudies = () => {
  return (
    <section id="case-studies" className="section-padding bg-background relative">
      {/* Background Decoration */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-accent to-transparent"></div>
        <div className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-accent to-transparent"></div>
      </div>
      
      <div className="container-custom relative z-10">
        <SectionHeader 
          title="Clientes que ya están en el Futuro"
          subtitle="Descubre cómo nuestras soluciones de IA han transformado negocios reales, generando resultados medibles y un impacto duradero."
        />
        
        <div className="space-y-0">
          {caseStudies.map((study, index) => (
            <CaseStudyCard 
              key={study.id}
              image={study.image}
              title={study.title}
              description={study.description}
              quote={study.quote}
              author={study.author}
              metrics={study.metrics}
              reverse={index % 2 !== 0}
            />
          ))}
        </div>
        
        {/* Call to Action */}
        <div className="text-center mt-16">
          <div className="glass-effect p-8 rounded-2xl max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold mb-4 gradient-text">
              ¿Tu negocio será el próximo caso de éxito?
            </h3>
            <p className="text-text-secondary mb-6">
              Únete a empresas líderes que ya están aprovechando el poder de la IA para crecer y destacar en su industria.
            </p>
            <div className="flex justify-center space-x-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-accent">100%</div>
                <div className="text-sm text-text-secondary">Satisfacción</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-accent">24/7</div>
                <div className="text-sm text-text-secondary">Soporte</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-accent">30+</div>
                <div className="text-sm text-text-secondary">Proyectos</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CaseStudies;
