import { heroContent } from '../../constants.jsx';
import AnimatedButton from '../ui/AnimatedButton';

const Hero = () => {
  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0">
        {/* Grid Pattern */}
        <div className="absolute inset-0 bg-[linear-gradient(to_right,#8080800a_1px,transparent_1px),linear-gradient(to_bottom,#8080800a_1px,transparent_1px)] bg-[size:14px_24px]"></div>
        
        {/* Floating Orbs */}
        <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-accent rounded-full animate-subtle-pulse"></div>
        <div className="absolute top-1/3 right-1/3 w-1 h-1 bg-accent-hover rounded-full animate-subtle-pulse" style={{ animationDelay: '1s' }}></div>
        <div className="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-accent rounded-full animate-subtle-pulse" style={{ animationDelay: '0.5s' }}></div>
        <div className="absolute top-1/2 right-1/4 w-1 h-1 bg-accent-hover rounded-full animate-subtle-pulse" style={{ animationDelay: '0.7s' }}></div>
        <div className="absolute bottom-1/3 right-1/2 w-2 h-2 bg-accent rounded-full animate-subtle-pulse" style={{ animationDelay: '0.3s' }}></div>
        
        {/* Large Gradient Orb */}
        <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] rounded-full bg-gradient-to-r from-accent/10 to-accent-hover/10 blur-3xl animate-float"></div>
      </div>
      
      {/* Content */}
      <div className="relative z-10 text-center max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 animate-fade-in-up">
          {heroContent.title}{' '}
          <span className="gradient-text">
            {heroContent.titleAccent}
          </span>
        </h1>
        
        <p className="text-xl md:text-2xl text-text-secondary mb-12 max-w-3xl mx-auto leading-relaxed animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
          {heroContent.subtitle}
        </p>
        
        <div className="animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
          <AnimatedButton 
            onClick={() => scrollToSection('services')}
            size="lg"
            className="animate-glow"
          >
            {heroContent.ctaText}
          </AnimatedButton>
        </div>
        
        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-accent rounded-full flex justify-center">
            <div className="w-1 h-3 bg-accent rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
