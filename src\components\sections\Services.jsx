import { services } from '../../constants.jsx';
import SectionHeader from '../ui/SectionHeader';
import ServiceCard from '../ui/ServiceCard';

const Services = () => {
  return (
    <section id="services" className="section-padding bg-gradient-to-b from-background via-primary/20 to-background relative overflow-hidden">
      {/* Enhanced Background Effects */}
      <div className="absolute inset-0">
        {/* Animated Grid Pattern */}
        <div className="absolute inset-0 bg-[linear-gradient(to_right,#A78BFA0a_1px,transparent_1px),linear-gradient(to_bottom,#A78BFA0a_1px,transparent_1px)] bg-[size:40px_40px] animate-pulse"></div>

        {/* Floating Orbs */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-accent/10 rounded-full blur-2xl animate-float"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-accent-hover/10 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }}></div>

        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-accent/5 to-transparent"></div>
      </div>

      <div className="container-custom relative z-10">
        <SectionHeader
          title="Nuestros Servicios de Automatización"
          subtitle="Transformamos tu negocio con soluciones de IA personalizadas que impulsan el crecimiento y optimizan la eficiencia operativa."
        />

        {/* Enhanced Grid with Better Spacing */}
        <div className="grid md:grid-cols-3 gap-8 lg:gap-12 mb-16">
          {services.map((service, index) => (
            <ServiceCard
              key={service.id}
              icon={service.icon}
              title={service.title}
              description={service.description}
              delay={index * 200}
            />
          ))}
        </div>

        {/* Enhanced Bottom Section */}
        <div className="text-center">
          {/* Stats Section */}
          <div className="grid grid-cols-3 gap-8 mb-12 max-w-2xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-bold gradient-text mb-2">100%</div>
              <div className="text-text-secondary text-sm">Automatización</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold gradient-text mb-2">24/7</div>
              <div className="text-text-secondary text-sm">Disponibilidad</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold gradient-text mb-2">∞</div>
              <div className="text-text-secondary text-sm">Escalabilidad</div>
            </div>
          </div>

          {/* Decorative Separator */}
          <div className="flex items-center justify-center space-x-4 text-text-secondary">
            <div className="w-16 h-px bg-gradient-to-r from-transparent via-accent to-accent"></div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-accent rounded-full animate-pulse"></div>
              <span className="text-sm font-medium tracking-wider">SOLUCIONES PERSONALIZADAS</span>
              <div className="w-2 h-2 bg-accent rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
            </div>
            <div className="w-16 h-px bg-gradient-to-l from-accent via-accent to-transparent"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Services;
